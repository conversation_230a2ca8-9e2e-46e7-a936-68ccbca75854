# Frontend Testing Guide - Eureka Integration

This guide provides comprehensive testing instructions for the airline management frontend with Eureka service discovery.

## 🎯 Testing Overview

The frontend now uses **Eureka service discovery** to dynamically find and connect to microservices. This means:
- ✅ Only Eureka server URL is hardcoded (`http://localhost:8761`)
- ✅ All other services are discovered automatically
- ✅ JWT tokens are stored in browser localStorage
- ✅ Real database data is displayed instead of dummy data

## 🔧 Prerequisites

Before testing, ensure all services are running:

### Required Services
1. **Eureka Server** - `http://localhost:8761`
2. **Backend1 (Auth)** - `http://localhost:8080` 
3. **Flights Service** - `http://localhost:8081`
4. **Passengers Service** - `http://localhost:8082`
5. **User Management** - `http://localhost:8083`
6. **Service Management** - `http://localhost:8084`
7. **Travel History** - `http://localhost:8085`

### Database Data
Based on `Database/airline-db-schema.sql`, the system should have:
- **4 Users**: admin1, inflight1, checkin1, passenger1
- **3 Flights**: Flight 101 (NYC-LON), Flight 202 (PAR-TOK), Flight 303 (LAX-SYD)
- **6 Passengers**: Alice Johnson, Bob <PERSON>, Charlie <PERSON>, Diana Prince, <PERSON>, <PERSON> Glenanne
- **8 Travel History Records**: Various completed, checked-in, cancelled, and pending trips

## 🧪 Testing Steps

### Step 1: Service Discovery Verification

1. **Run Integration Test**:
   ```bash
   cd frontend
   node test-integration.js
   ```

2. **Expected Results**:
   - ✅ Eureka Server: OK
   - ✅ 6 services discovered
   - ✅ At least 3/6 services healthy

### Step 2: Frontend Application Testing

1. **Start Frontend**:
   ```bash
   npm run dev
   ```

2. **Open Browser**: http://localhost:5173/

### Step 3: Login Testing

**Test Credentials** (from database schema):
- **Admin**: `admin1` / `adminpass`
- **Inflight Staff**: `inflight1` / `inflightpass`
- **Checkin Staff**: `checkin1` / `checkinpass`
- **Passenger**: `passenger1` / `passpass`

**Login Test Checklist**:
- [ ] Login form appears
- [ ] Valid credentials redirect to appropriate dashboard
- [ ] Invalid credentials show error message
- [ ] JWT token is stored in localStorage
- [ ] User info is stored in localStorage

### Step 4: Admin Dashboard Testing

**Access**: Login as `admin1` / `adminpass`

**Admin Features to Test**:
- [ ] **Service Registry**: Shows all discovered microservices
- [ ] **JWT Token Verification**: Displays token info and validation
- [ ] **Manage Flights**: Shows real flight data from database
- [ ] **Manage Passengers**: Shows real passenger data
- [ ] **Travel History**: Shows real travel records
- [ ] **Manage Routes**: Route management functionality

**Expected Data Verification**:

#### Flights Data
Should display 3 flights matching database:
- **Flight 101**: NYC-LON, 2025-08-20, Boeing 747, 20 seats, 17 available
- **Flight 202**: PAR-TOK, 2025-08-21, Airbus A380, 30 seats, 28 available  
- **Flight 303**: LAX-SYD, 2025-08-22, Boeing 777, 30 seats, 29 available

#### Passengers Data
Should display 6 passengers:
- **Alice Johnson**: Flight 101, Seat 12A, Checked-in, Veg meal
- **Bob Smith**: Flight 101, Seat 14B, Checked-in, Wheelchair
- **Charlie Brown**: Flight 101, Seat 15C, Checked-in, With infant
- **Diana Prince**: Flight 202, Seat 10A, Checked-in, Veg meal
- **Ethan Hunt**: Flight 202, Seat 11B, Checked-in
- **Fiona Glenanne**: Flight 303, Seat 5C, Checked-in, Wheelchair + infant

### Step 5: Staff Dashboard Testing

**Inflight Staff**: Login as `inflight1` / `inflightpass`
**Checkin Staff**: Login as `checkin1` / `checkinpass`

**Staff Features to Test**:
- [ ] Staff dashboard loads
- [ ] Flight assignment information
- [ ] Passenger management for assigned flight
- [ ] Service management capabilities

### Step 6: Passenger Dashboard Testing

**Access**: Login as `passenger1` / `passpass`

**Passenger Features to Test**:
- [ ] Passenger dashboard loads
- [ ] Flight booking interface
- [ ] Personal travel history
- [ ] Service selection

### Step 7: JWT Token Verification

**Access**: Admin → JWT Token Verification

**Token Verification Checklist**:
- [ ] Token presence indicator
- [ ] Token length and preview
- [ ] Stored user information
- [ ] JWT payload decode
- [ ] Token expiration time
- [ ] Token validation against backend
- [ ] Clear token functionality

### Step 8: Service Registry Verification

**Access**: Admin → Service Registry

**Service Registry Checklist**:
- [ ] All 6 microservices listed
- [ ] Service URLs from Eureka
- [ ] Health status indicators
- [ ] Instance count information
- [ ] Auto-refresh functionality
- [ ] Service discovery timestamp

## 🔍 Data Validation

### Database vs Frontend Comparison

**Users Table Validation**:
- [ ] admin1 → Admin role, name "Alice"
- [ ] inflight1 → Inflight staff, name "Charlie Inflight", flight_id 1
- [ ] checkin1 → Checkin staff, name "Diana Checkin", flight_id 2
- [ ] passenger1 → Passenger, name "Sam Traveler"

**Flights Table Validation**:
- [ ] Flight names match (Flight 101, 202, 303)
- [ ] Routes match (NYC-LON, PAR-TOK, LAX-SYD)
- [ ] Dates match (2025-08-20, 2025-08-21, 2025-08-22)
- [ ] Aircraft types match (Boeing 747, Airbus A380, Boeing 777)
- [ ] Seat counts match (20, 30, 30 total seats)

**Passengers Table Validation**:
- [ ] All 6 passengers displayed
- [ ] Flight assignments correct
- [ ] Seat assignments match
- [ ] Special needs indicators (wheelchair, infant)
- [ ] Service selections match
- [ ] Check-in status correct

**Travel History Validation**:
- [ ] 8 travel history records
- [ ] Booking references match (ABC123, DEF456, etc.)
- [ ] Status values correct (Completed, Checked-in, Cancelled, Pending)
- [ ] Distance and duration data
- [ ] Notes field content

## 🚨 Common Issues & Solutions

### Service Discovery Issues
- **Problem**: Services not discovered
- **Solution**: Check Eureka server, restart microservices

### Authentication Issues  
- **Problem**: Login fails
- **Solution**: Verify database credentials, check JWT configuration

### Data Loading Issues
- **Problem**: No data displayed
- **Solution**: Check microservice health, verify database connection

### Token Issues
- **Problem**: Token validation fails
- **Solution**: Check JWT secret consistency across services

## ✅ Success Criteria

The integration is successful when:
- [ ] All 6 microservices discovered by Eureka
- [ ] Login works with database credentials
- [ ] JWT tokens stored and validated correctly
- [ ] Real database data displayed in all pages
- [ ] Service registry shows healthy services
- [ ] All user roles function correctly
- [ ] Data matches database schema exactly

## 📊 Test Results Template

```
Date: ___________
Tester: ___________

Service Discovery: ✅/❌
Login Functionality: ✅/❌
Admin Dashboard: ✅/❌
Staff Dashboard: ✅/❌
Passenger Dashboard: ✅/❌
JWT Token Management: ✅/❌
Data Accuracy: ✅/❌
Service Registry: ✅/❌

Overall Status: ✅/❌
Notes: ___________
```
