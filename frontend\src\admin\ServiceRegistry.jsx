import { useState, useEffect } from 'react';
import eurekaApiClient from '../api/eurekaApiClient';

export default function ServiceRegistry() {
  const [services, setServices] = useState({});
  const [healthStatus, setHealthStatus] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadServiceRegistry();
    const interval = setInterval(loadServiceRegistry, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadServiceRegistry = async () => {
    try {
      setLoading(true);
      const [servicesData, healthData] = await Promise.all([
        eurekaApiClient.getServiceRegistry(),
        eurekaApiClient.healthCheck()
      ]);
      
      setServices(servicesData);
      setHealthStatus(healthData);
      setError('');
    } catch (err) {
      setError('Failed to load service registry: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'UP': return 'text-green-600 bg-green-100';
      case 'DOWN': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading service registry...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-4">
        <div className="text-red-800">{error}</div>
        <button 
          onClick={loadServiceRegistry} 
          className="mt-2 px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Service Registry</h2>
        <button 
          onClick={loadServiceRegistry}
          className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Refresh
        </button>
      </div>

      <div className="grid gap-4">
        {Object.entries(services).length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No services discovered from Eureka server
          </div>
        ) : (
          Object.entries(services).map(([serviceName, serviceInfo]) => {
            const health = healthStatus[serviceName] || {};
            const isHealthy = health.status === 'UP';
            
            return (
              <div key={serviceName} className="bg-white rounded-lg shadow p-4 border">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="text-lg font-medium">{serviceInfo.name}</h3>
                    <p className="text-sm text-gray-600">{serviceInfo.url}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(serviceInfo.status)}`}>
                      {serviceInfo.status}
                    </span>
                    {health.status && (
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(health.status)}`}>
                        Health: {health.status}
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Instances:</span> {serviceInfo.instances}
                  </div>
                  <div>
                    <span className="font-medium">Service Name:</span> {serviceName}
                  </div>
                </div>

                {health.error && (
                  <div className="mt-2 p-2 bg-red-50 rounded text-sm text-red-700">
                    <strong>Health Check Error:</strong> {health.error}
                  </div>
                )}

                {health.health && (
                  <div className="mt-2 p-2 bg-green-50 rounded text-sm">
                    <strong>Health Details:</strong>
                    <pre className="mt-1 text-xs overflow-x-auto">
                      {JSON.stringify(health.health, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded">
        <h3 className="font-medium mb-2">Service Discovery Info</h3>
        <p className="text-sm text-gray-700">
          Services are automatically discovered from Eureka server at <code>http://localhost:8761</code>.
          The frontend dynamically routes requests to discovered service instances.
        </p>
        <div className="mt-2 text-xs text-gray-600">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
}
