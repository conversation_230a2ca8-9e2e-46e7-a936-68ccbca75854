import axios from 'axios';

class EurekaService {
  constructor() {
    this.eurekaUrl = 'http://localhost:8761';
    this.serviceRegistry = new Map();
    this.lastFetch = 0;
    this.cacheDuration = 30000; // 30 seconds cache
  }

  // Fetch service registry from Eureka
  async fetchServiceRegistry() {
    try {
      const response = await axios.get(`${this.eurekaUrl}/eureka/apps`, {
        headers: {
          'Accept': 'application/json'
        },
        timeout: 5000
      });

      const applications = response.data?.applications?.application || [];
      const services = new Map();

      applications.forEach(app => {
        const instances = Array.isArray(app.instance) ? app.instance : [app.instance];
        const healthyInstances = instances.filter(instance => 
          instance.status === 'UP' && instance.port && instance.port['$']
        );

        if (healthyInstances.length > 0) {
          // Use the first healthy instance (can implement load balancing later)
          const instance = healthyInstances[0];
          const serviceUrl = `http://${instance.ipAddr}:${instance.port['$']}`;
          
          services.set(app.name.toLowerCase(), {
            name: app.name,
            url: serviceUrl,
            instances: healthyInstances.length,
            status: 'UP'
          });
        }
      });

      this.serviceRegistry = services;
      this.lastFetch = Date.now();
      
      console.log('🔍 Service Registry Updated:', Object.fromEntries(services));
      return services;
    } catch (error) {
      console.error('❌ Failed to fetch service registry from Eureka:', error.message);
      throw new Error(`Eureka service discovery failed: ${error.message}`);
    }
  }

  // Get service URL by name
  async getServiceUrl(serviceName) {
    const now = Date.now();
    
    // Refresh cache if expired or empty
    if (this.serviceRegistry.size === 0 || (now - this.lastFetch) > this.cacheDuration) {
      await this.fetchServiceRegistry();
    }

    const service = this.serviceRegistry.get(serviceName.toLowerCase());
    if (!service) {
      throw new Error(`Service '${serviceName}' not found in registry`);
    }

    return service.url;
  }

  // Get all registered services
  async getAllServices() {
    const now = Date.now();
    
    if (this.serviceRegistry.size === 0 || (now - this.lastFetch) > this.cacheDuration) {
      await this.fetchServiceRegistry();
    }

    return Object.fromEntries(this.serviceRegistry);
  }

  // Check if a service is available
  async isServiceAvailable(serviceName) {
    try {
      const service = this.serviceRegistry.get(serviceName.toLowerCase());
      return service && service.status === 'UP';
    } catch (error) {
      return false;
    }
  }

  // Make HTTP request to a discovered service
  async makeServiceRequest(serviceName, endpoint, options = {}) {
    try {
      const serviceUrl = await this.getServiceUrl(serviceName);
      const fullUrl = `${serviceUrl}${endpoint}`;
      
      const config = {
        url: fullUrl,
        method: options.method || 'GET',
        timeout: options.timeout || 10000,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      };

      if (options.data) {
        config.data = options.data;
      }

      if (options.params) {
        config.params = options.params;
      }

      const response = await axios(config);
      return response;
    } catch (error) {
      console.error(`❌ Service request failed for ${serviceName}${endpoint}:`, error.message);
      throw error;
    }
  }

  // Service name mappings for our microservices
  getServiceMapping() {
    return {
      'auth': 'backend1',
      'flights': 'flights',
      'passengers': 'passengers', 
      'users': 'usermanagement',
      'services': 'service-management',
      'history': 'travel-history-service'
    };
  }

  // Get actual service name from logical name
  getActualServiceName(logicalName) {
    const mapping = this.getServiceMapping();
    return mapping[logicalName] || logicalName;
  }

  // Health check for all services
  async healthCheck() {
    try {
      const services = await this.getAllServices();
      const healthStatus = {};

      for (const [name, service] of Object.entries(services)) {
        try {
          const response = await axios.get(`${service.url}/actuator/health`, { timeout: 3000 });
          healthStatus[name] = {
            status: 'UP',
            url: service.url,
            health: response.data
          };
        } catch (error) {
          healthStatus[name] = {
            status: 'DOWN',
            url: service.url,
            error: error.message
          };
        }
      }

      return healthStatus;
    } catch (error) {
      console.error('Health check failed:', error);
      return {};
    }
  }
}

export default new EurekaService();
