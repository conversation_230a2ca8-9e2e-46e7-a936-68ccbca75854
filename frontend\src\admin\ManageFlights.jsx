import { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import * as service from './flightService'

function FlightForm({ onSave, onCancel, initial = {}, routes = [] }) {
  const [f, setF] = useState({
    id: initial.id || null,
    name: initial.name || '',
    route: initial.route || '',
    date: initial.date || '',
  })

  function change(k, v) { setF(prev => ({ ...prev, [k]: v })) }

  return (
    <form onSubmit={e => { e.preventDefault(); onSave(f) }} className="grid grid-cols-2 gap-2 mb-3">
      <input placeholder="Name" value={f.name} onChange={e => change('name', e.target.value)} className="border p-2 rounded" />
      <select value={f.route} onChange={e => change('route', e.target.value)} className="border p-2 rounded">
        <option value="">Select route</option>
        {routes.map(r => <option key={r.id} value={r.route}>{r.route}</option>)}
      </select>
      <input placeholder="Date" type="date" value={f.date} onChange={e => change('date', e.target.value)} className="border p-2 rounded" />
      <div className="flex gap-2">
        <button type="button" onClick={onCancel} className="px-3 py-1 bg-gray-200 rounded">Cancel</button>
        <button type="submit" className="px-3 py-1 bg-green-500 text-white rounded">Save Flight</button>
      </div>
    </form>
  )
}

export default function ManageFlights() {
  const [flights, setFlights] = useState([])
  const [search, setSearch] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const nav = useNavigate()

  useEffect(() => {
    // Load initial flights
    const loadFlights = async () => {
      try {
        setLoading(true)
        const flightsList = await service.list()
        setFlights(flightsList)
        setError('')
      } catch (err) {
        setError('Failed to load flights: ' + err.message)
      } finally {
        setLoading(false)
      }
    }

    loadFlights()

    // Subscribe to flight updates
    const unsubscribe = service.subscribe(list => setFlights(list))
    return unsubscribe
  }, [])

  const displayed = flights.filter(f => {
    if (!search) return true
    const q = search.toLowerCase()
    return (f.name?.toLowerCase().includes(q) || f.route?.toLowerCase().includes(q))
  })

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading flights...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-4">
        <div className="text-red-800">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-3">
          <h2 className="text-lg font-semibold">Flights</h2>
          <input value={search} onChange={e => setSearch(e.target.value)} placeholder="Search flights by name or route" className="border p-2 rounded w-80" />
        </div>
        <button className="px-3 py-1 bg-green-500 text-white rounded" onClick={() => nav('/admin/flights/new')}>Add Flight</button>
      </div>

      <div className="overflow-x-auto bg-white rounded p-2">
        <table className="w-full table-auto border-collapse">
          <thead>
            <tr className="text-left border-b">
              <th className="p-2">Name</th>
              <th className="p-2">Route</th>
              <th className="p-2">Date</th>
              <th className="p-2">Services</th>
            </tr>
          </thead>
          <tbody>
            {displayed.length === 0 ? (
              <tr>
                <td colSpan="4" className="p-4 text-center text-gray-500">
                  {search ? 'No flights found matching your search.' : 'No flights available.'}
                </td>
              </tr>
            ) : (
              displayed.map(f => (
                <tr key={f.id} className="border-b">
                  <td className="p-2"><Link to={`/admin/flights/${f.id}`} className="text-indigo-600">{f.name}</Link></td>
                  <td className="p-2">{f.route}</td>
                  <td className="p-2">{f.date || '-'}</td>
                  <td className="p-2">{(f.services || []).join(', ') || '-'}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
