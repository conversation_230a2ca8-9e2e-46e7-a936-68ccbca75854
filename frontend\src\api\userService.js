import apiClient from './apiClient';

class UserService {
  // Get all users
  async getAllUsers() {
    try {
      const response = await apiClient.get('/users');
      return response.data.data; // API returns wrapped response
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users');
    }
  }

  // Get user by ID
  async getUserById(id) {
    try {
      const response = await apiClient.get(`/users/${id}`);
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user');
    }
  }

  // Get user by username
  async getUserByUsername(username) {
    try {
      const response = await apiClient.get(`/users/username/${username}`);
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user by username');
    }
  }

  // Create new user
  async createUser(userData) {
    try {
      const response = await apiClient.post('/users', userData);
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create user');
    }
  }

  // Update user
  async updateUser(id, userData) {
    try {
      const response = await apiClient.put(`/users/${id}`, userData);
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update user');
    }
  }

  // Delete user
  async deleteUser(id) {
    try {
      await apiClient.delete(`/users/${id}`);
      return true;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete user');
    }
  }

  // Get users by role
  async getUsersByRole(role) {
    try {
      const response = await apiClient.get(`/users/role/${role}`);
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users by role');
    }
  }

  // Get staff users
  async getStaffUsers() {
    try {
      const response = await apiClient.get('/users/staff');
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch staff users');
    }
  }

  // Get users by flight (for staff)
  async getUsersByFlight(flightId) {
    try {
      const response = await apiClient.get(`/users/flight/${flightId}`);
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users by flight');
    }
  }

  // Search users
  async searchUsers(searchTerm) {
    try {
      const response = await apiClient.get(`/users/search?q=${encodeURIComponent(searchTerm)}`);
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to search users');
    }
  }

  // Update user password
  async updatePassword(id, oldPassword, newPassword) {
    try {
      const response = await apiClient.put(`/users/${id}/password`, {
        oldPassword,
        newPassword
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update password');
    }
  }

  // Activate/Deactivate user
  async toggleUserStatus(id, active) {
    try {
      const response = await apiClient.put(`/users/${id}/status`, { active });
      return response.data.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update user status');
    }
  }
}

export default new UserService();
