import apiClient from './apiClient';

class ServiceManagementService {
  // Get all services
  async getAllServices() {
    try {
      const response = await apiClient.get('/services');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch services');
    }
  }

  // Get service by ID
  async getServiceById(id) {
    try {
      const response = await apiClient.get(`/services/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch service');
    }
  }

  // Create new service
  async createService(serviceData) {
    try {
      const response = await apiClient.post('/services', serviceData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create service');
    }
  }

  // Update service
  async updateService(id, serviceData) {
    try {
      const response = await apiClient.put(`/services/${id}`, serviceData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update service');
    }
  }

  // Delete service
  async deleteService(id) {
    try {
      await apiClient.delete(`/services/${id}`);
      return true;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete service');
    }
  }

  // Get services by flight
  async getServicesByFlight(flightId) {
    try {
      const response = await apiClient.get(`/services/flight/${flightId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch services by flight');
    }
  }

  // Get services by passenger
  async getServicesByPassenger(passengerId) {
    try {
      const response = await apiClient.get(`/services/passenger/${passengerId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch services by passenger');
    }
  }

  // Get flight passenger services
  async getFlightPassengerServices(flightId, serviceType = null) {
    try {
      let url = `/services/flight/${flightId}/passengers`;
      if (serviceType) {
        url += `?serviceType=${serviceType}`;
      }
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch flight passenger services');
    }
  }

  // Get flight service statistics
  async getFlightServiceStats(flightId) {
    try {
      const response = await apiClient.get(`/services/flight/${flightId}/stats`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch flight service statistics');
    }
  }

  // Meal Services
  async getMealServices(flightId, mealType = null) {
    try {
      let url = `/services/meals/flight/${flightId}`;
      if (mealType) {
        url += `?mealType=${mealType}`;
      }
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch meal services');
    }
  }

  // Baggage Services
  async getBaggageServices(flightId) {
    try {
      const response = await apiClient.get(`/services/baggage/flight/${flightId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch baggage services');
    }
  }

  // Shopping Services
  async getShoppingServices(flightId) {
    try {
      const response = await apiClient.get(`/services/shopping/flight/${flightId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch shopping services');
    }
  }

  // Update service status
  async updateServiceStatus(id, status) {
    try {
      const response = await apiClient.put(`/services/${id}/status`, { status });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update service status');
    }
  }
}

export default new ServiceManagementService();
