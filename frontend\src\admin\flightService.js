import { flightService } from '../api';

// Cache for flights data
let flightsCache = [];
const listeners = new Set();

// Load flights from API
async function loadFlights() {
  try {
    flightsCache = await flightService.getAllFlights();
    emit();
    return flightsCache;
  } catch (error) {
    console.error('Failed to load flights:', error);
    throw error;
  }
}

// Get all flights
export async function list() {
  if (flightsCache.length === 0) {
    await loadFlights();
  }
  return flightsCache.slice();
}

// Find flight by ID
export async function findById(id) {
  try {
    return await flightService.getFlightById(Number(id));
  } catch (error) {
    console.error('Failed to find flight by ID:', error);
    // Fallback to cache
    return flightsCache.find(f => f.id === Number(id));
  }
}

// Add or update flight
export async function addOrUpdate(f) {
  try {
    let updatedFlight;
    if (f.id) {
      updatedFlight = await flightService.updateFlight(f.id, f);
      flightsCache = flightsCache.map(x => x.id === f.id ? updatedFlight : x);
    } else {
      updatedFlight = await flightService.createFlight(f);
      flightsCache = [...flightsCache, updatedFlight];
    }
    emit();
    return updatedFlight;
  } catch (error) {
    console.error('Failed to add/update flight:', error);
    throw error;
  }
}

// Remove flight
export async function remove(id) {
  try {
    await flightService.deleteFlight(Number(id));
    flightsCache = flightsCache.filter(f => f.id !== Number(id));
    emit();
    return true;
  } catch (error) {
    console.error('Failed to remove flight:', error);
    throw error;
  }
}

// Get flights by route
export async function getByRoute(route) {
  try {
    return await flightService.getFlightsByRoute(route);
  } catch (error) {
    console.error('Failed to get flights by route:', error);
    throw error;
  }
}

// Get flights by date
export async function getByDate(date) {
  try {
    return await flightService.getFlightsByDate(date);
  } catch (error) {
    console.error('Failed to get flights by date:', error);
    throw error;
  }
}

// Get available flights
export async function getAvailable() {
  try {
    return await flightService.getFlightsWithAvailableSeats();
  } catch (error) {
    console.error('Failed to get available flights:', error);
    throw error;
  }
}

// Event system for reactive updates
function emit() {
  listeners.forEach(cb => cb(flightsCache.slice()));
}

export function subscribe(cb) {
  listeners.add(cb);
  return () => listeners.delete(cb);
}

// Initialize flights on module load
loadFlights().catch(console.error);
