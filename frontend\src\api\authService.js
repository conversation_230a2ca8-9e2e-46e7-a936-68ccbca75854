import eurekaApiClient from './eurekaApiClient';

class AuthService {
  // Login user
  async login(username, password) {
    try {
      const response = await eurekaApiClient.login(username, password);

      if (response.data.success !== false) {
        // Store token and user info
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('user', JSON.stringify({
          username: response.data.username,
          role: response.data.role,
          name: response.data.name
        }));
        return response.data;
      } else {
        throw new Error(response.data.message || 'Login failed');
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || error.message || 'Login failed');
    }
  }

  // Logout user
  logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  }

  // Get current user
  getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!localStorage.getItem('authToken');
  }

  // Get auth token
  getToken() {
    return localStorage.getItem('authToken');
  }

  // Validate token
  async validateToken() {
    try {
      const response = await eurekaApiClient.validateToken();
      return response.data;
    } catch (error) {
      this.logout();
      throw error;
    }
  }

  // Get user roles
  async getUserRoles() {
    try {
      const response = await eurekaApiClient.getUserRoles();
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get user roles');
    }
  }

  // Check if user has specific role
  hasRole(role) {
    const user = this.getCurrentUser();
    return user && user.role === role;
  }

  // Check if user is admin
  isAdmin() {
    return this.hasRole('admin');
  }

  // Check if user is staff
  isStaff() {
    const user = this.getCurrentUser();
    return user && (user.role === 'inflightStaff' || user.role === 'checkinStaff');
  }

  // Check if user is passenger
  isPassenger() {
    return this.hasRole('passenger');
  }
}

export default new AuthService();
