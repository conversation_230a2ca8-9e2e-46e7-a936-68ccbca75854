spring.application.name=eureka-server

# Server Configuration
server.port=8761

# Eureka Server Configuration
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/

# Eureka Server Dashboard
eureka.server.enable-self-preservation=false
eureka.server.eviction-interval-timer-in-ms=5000

# Logging
logging.level.com.netflix.eureka=DEBUG
logging.level.com.netflix.discovery=DEBUG
