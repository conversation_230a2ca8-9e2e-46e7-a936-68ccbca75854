// Temporary dummy data for components that haven't been updated yet
// This will be replaced with real API calls

export const users = [
  { id: 1, username: 'admin1', password: 'adminpass', role: 'admin', name: '<PERSON>' },
  { id: 2, username: 'inflight1', password: 'inflightpass', role: 'inflightStaff', name: '<PERSON> Inflight', flightId: 1 },
  { id: 3, username: 'checkin1', password: 'checkinpass', role: 'checkinStaff', name: '<PERSON>', flightId: 2 },
  { id: 4, username: 'passenger1', password: 'passpass', role: 'passenger', name: '<PERSON> Traveler' }
];

export const flights = [
  {
    id: 1,
    name: 'Flight 101',
    route: 'NYC-LON',
    date: '2025-08-20',
    departureTime: '08:00 AM',
    arrivalTime: '04:00 PM',
    aircraftType: 'Boeing 747',
    totalSeats: 20,
    availableSeats: 17,
    services: ['Ancillary', 'Meal', 'Shopping'],
    serviceSubtypes: {
      Ancillary: ['Extra Baggage 5kg', 'Extra Baggage 10kg', 'Priority Boarding'],
      Meal: ['Veg', 'Non-Veg', 'Vegan', 'Gluten-Free'],
      Shopping: ['Magazine', 'Perfume', 'Sunglasses', 'Headphones']
    },
    seatMap: Array.from({length: 20}, (_, i) => ({
      number: i + 1,
      isBooked: [12, 14, 15].includes(i + 1)
    }))
  },
  {
    id: 2,
    name: 'Flight 202',
    route: 'PAR-TOK',
    date: '2025-08-21',
    departureTime: '09:00 AM',
    arrivalTime: '11:00 PM',
    aircraftType: 'Airbus A380',
    totalSeats: 30,
    availableSeats: 28,
    services: ['Ancillary', 'Meal'],
    serviceSubtypes: {
      Ancillary: ['Extra Baggage 5kg', 'Priority Boarding'],
      Meal: ['Veg', 'Non-Veg', 'Kosher']
    },
    seatMap: Array.from({length: 30}, (_, i) => ({
      number: i + 1,
      isBooked: [10, 11].includes(i + 1)
    }))
  },
  {
    id: 3,
    name: 'Flight 303',
    route: 'LAX-SYD',
    date: '2025-08-22',
    departureTime: '10:00 AM',
    arrivalTime: '06:00 AM',
    aircraftType: 'Boeing 777',
    totalSeats: 30,
    availableSeats: 29,
    services: ['Shopping'],
    serviceSubtypes: {
      Shopping: ['Souvenir', 'Duty-Free Liquor', 'Travel Adapter']
    },
    seatMap: Array.from({length: 30}, (_, i) => ({
      number: i + 1,
      isBooked: [5].includes(i + 1)
    }))
  }
];

export const passengers = [
  {
    id: 1,
    flightId: 1,
    name: 'Alice Johnson',
    phoneNumber: '************',
    address: '123 Main St, New York, NY',
    passportNumber: 'A1234567',
    dateOfBirth: '1990-04-15',
    from: 'NYC',
    to: 'LON',
    services: ['Meal', 'Ancillary'],
    mealType: 'Veg',
    mealName: 'Biryani',
    extraBaggage: 10,
    shoppingItems: [],
    seat: '12A',
    checkedIn: true,
    wheelchair: false,
    infant: false
  },
  {
    id: 2,
    flightId: 1,
    name: 'Bob Smith',
    phoneNumber: '************',
    address: '456 Elm St, Los Angeles, CA',
    passportNumber: null,
    dateOfBirth: null,
    from: 'NYC',
    to: 'LON',
    services: ['Shopping'],
    mealType: null,
    mealName: null,
    extraBaggage: 0,
    shoppingItems: ['Magazine', 'Perfume'],
    seat: '14B',
    checkedIn: true,
    wheelchair: true,
    infant: false
  },
  {
    id: 3,
    flightId: 1,
    name: 'Charlie Brown',
    phoneNumber: '************',
    address: '789 Oak St, Chicago, IL',
    passportNumber: 'B7654321',
    dateOfBirth: '1985-11-05',
    from: 'NYC',
    to: 'LON',
    services: ['Meal', 'Shopping'],
    mealType: 'Non-Veg',
    mealName: 'Burger',
    extraBaggage: 0,
    shoppingItems: ['Chocolates'],
    seat: '15C',
    checkedIn: true,
    wheelchair: false,
    infant: true
  },
  {
    id: 4,
    flightId: 2,
    name: 'Diana Prince',
    phoneNumber: '************',
    address: '321 Maple St, Paris, FR',
    passportNumber: 'P9998887',
    dateOfBirth: '1992-07-20',
    from: 'PAR',
    to: 'TOK',
    services: ['Meal'],
    mealType: 'Veg',
    mealName: 'Salad',
    extraBaggage: 0,
    shoppingItems: [],
    seat: '10A',
    checkedIn: true,
    wheelchair: false,
    infant: false
  },
  {
    id: 5,
    flightId: 2,
    name: 'Ethan Hunt',
    phoneNumber: '************',
    address: '654 Pine St, Tokyo, JP',
    passportNumber: 'E5554443',
    dateOfBirth: '1978-03-12',
    from: 'PAR',
    to: 'TOK',
    services: ['Shopping'],
    mealType: null,
    mealName: null,
    extraBaggage: 0,
    shoppingItems: ['Watch'],
    seat: '11B',
    checkedIn: true,
    wheelchair: false,
    infant: false
  },
  {
    id: 6,
    flightId: 3,
    name: 'Fiona Glenanne',
    phoneNumber: '************',
    address: '987 Birch St, Sydney, AU',
    passportNumber: null,
    dateOfBirth: null,
    from: 'LAX',
    to: 'SYD',
    services: ['Ancillary'],
    mealType: null,
    mealName: null,
    extraBaggage: 15,
    shoppingItems: [],
    seat: '5C',
    checkedIn: true,
    wheelchair: true,
    infant: true
  }
];

export const travelHistory = [
  {
    id: 1,
    passengerId: 1,
    flightId: 1,
    travelDate: '2024-12-15',
    origin: 'NYC',
    destination: 'LON',
    seat: '12A',
    bookingReference: 'ABC123',
    fareClass: 'Economy',
    status: 'Completed',
    distanceKm: 5567,
    durationMin: 420,
    notes: 'On-time arrival'
  },
  {
    id: 2,
    passengerId: 2,
    flightId: 1,
    travelDate: '2025-01-10',
    origin: 'NYC',
    destination: 'LON',
    seat: '14B',
    bookingReference: 'DEF456',
    fareClass: 'Economy',
    status: 'Completed',
    distanceKm: 5567,
    durationMin: 430,
    notes: 'Delayed due to weather'
  },
  {
    id: 3,
    passengerId: 3,
    flightId: 1,
    travelDate: '2025-02-05',
    origin: 'NYC',
    destination: 'LON',
    seat: '15C',
    bookingReference: 'GHI789',
    fareClass: 'Business',
    status: 'Completed',
    distanceKm: 5567,
    durationMin: 415,
    notes: 'Upgraded to Business'
  },
  {
    id: 4,
    passengerId: 4,
    flightId: 2,
    travelDate: '2025-03-21',
    origin: 'PAR',
    destination: 'TOK',
    seat: '10A',
    bookingReference: 'JKL012',
    fareClass: 'Economy',
    status: 'Completed',
    distanceKm: 9712,
    durationMin: 840,
    notes: null
  },
  {
    id: 5,
    passengerId: 5,
    flightId: 2,
    travelDate: '2025-04-01',
    origin: 'PAR',
    destination: 'TOK',
    seat: '11B',
    bookingReference: 'MNO345',
    fareClass: 'Premium Economy',
    status: 'Checked-in',
    distanceKm: 9712,
    durationMin: 845,
    notes: 'Checked in online'
  },
  {
    id: 6,
    passengerId: 6,
    flightId: 3,
    travelDate: '2025-05-18',
    origin: 'LAX',
    destination: 'SYD',
    seat: '5C',
    bookingReference: 'PQR678',
    fareClass: 'Economy',
    status: 'Cancelled',
    distanceKm: 12051,
    durationMin: 900,
    notes: 'Cancelled by airline'
  }
];

export const routes = [
  { id: 1, route: 'NYC-LON', origin: 'New York', destination: 'London' },
  { id: 2, route: 'PAR-TOK', origin: 'Paris', destination: 'Tokyo' },
  { id: 3, route: 'LAX-SYD', origin: 'Los Angeles', destination: 'Sydney' }
];
