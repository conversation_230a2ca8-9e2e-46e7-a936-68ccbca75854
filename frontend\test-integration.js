// Integration test script to verify Eureka service discovery and microservices connectivity
import axios from 'axios';

const EUREKA_URL = 'http://localhost:8761';

// Test Eureka server and service discovery
async function testEurekaServiceDiscovery() {
  console.log('🔍 Testing Eureka Service Discovery...\n');

  try {
    // Test Eureka server
    const eurekaResponse = await axios.get(`${EUREKA_URL}/eureka/apps`, {
      headers: { 'Accept': 'application/json' },
      timeout: 5000
    });

    console.log('✅ Eureka Server: OK');

    // Parse registered services
    const applications = eurekaResponse.data?.applications?.application || [];
    const discoveredServices = new Map();

    applications.forEach(app => {
      const instances = Array.isArray(app.instance) ? app.instance : [app.instance];
      const healthyInstances = instances.filter(instance =>
        instance.status === 'UP' && instance.port && instance.port['$']
      );

      if (healthyInstances.length > 0) {
        const instance = healthyInstances[0];
        const serviceUrl = `http://${instance.ipAddr}:${instance.port['$']}`;
        discoveredServices.set(app.name.toLowerCase(), {
          name: app.name,
          url: serviceUrl,
          instances: healthyInstances.length
        });
      }
    });

    console.log(`📋 Discovered ${discoveredServices.size} services from Eureka:`);
    discoveredServices.forEach((service, name) => {
      console.log(`   • ${service.name}: ${service.url} (${service.instances} instances)`);
    });

    return discoveredServices;
  } catch (error) {
    console.error('❌ Eureka Server failed:', error.message);
    return new Map();
  }
}

// Test discovered services
async function testDiscoveredServices(discoveredServices) {
  console.log('\n🧪 Testing discovered services...\n');

  let successCount = 0;
  const totalServices = discoveredServices.size;

  for (const [serviceName, serviceInfo] of discoveredServices) {
    try {
      // Test basic connectivity
      const response = await axios.get(`${serviceInfo.url}/actuator/health`, {
        timeout: 3000
      });
      console.log(`✅ ${serviceInfo.name}: Health check OK (Status: ${response.status})`);
      successCount++;
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${serviceInfo.name}: Service not responding`);
      } else if (error.response) {
        console.log(`⚠️  ${serviceInfo.name}: HTTP ${error.response.status} - ${error.response.statusText}`);
      } else {
        console.log(`❌ ${serviceInfo.name}: ${error.message}`);
      }
    }

    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 300));
  }

  return { successCount, totalServices };
}

// Test frontend integration with Eureka
async function testFrontendIntegration() {
  console.log('\n🌐 Testing Frontend Integration...\n');

  try {
    // Test if frontend can access Eureka through proxy
    const response = await axios.get('http://localhost:5173/eureka/apps', {
      headers: { 'Accept': 'application/json' },
      timeout: 5000
    });

    console.log('✅ Frontend can access Eureka server through proxy');
    return true;
  } catch (error) {
    console.log('❌ Frontend cannot access Eureka server:', error.message);
    return false;
  }
}

// Main test runner
async function runIntegrationTests() {
  console.log('🚀 Starting Eureka-based Integration Tests...\n');
  console.log('=' .repeat(60));

  // Step 1: Test Eureka service discovery
  const discoveredServices = await testEurekaServiceDiscovery();

  if (discoveredServices.size === 0) {
    console.log('\n❌ No services discovered. Cannot proceed with integration tests.');
    console.log('\n📝 Please ensure:');
    console.log('   1. Eureka server is running on port 8761');
    console.log('   2. All microservices are started and registered');
    console.log('   3. Services are configured to register with Eureka');
    return;
  }

  // Step 2: Test discovered services
  const { successCount, totalServices } = await testDiscoveredServices(discoveredServices);

  // Step 3: Test frontend integration
  const frontendOk = await testFrontendIntegration();

  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 INTEGRATION TEST SUMMARY');
  console.log('=' .repeat(60));
  console.log(`🔍 Service Discovery: ${discoveredServices.size > 0 ? 'OK' : 'FAILED'}`);
  console.log(`🏥 Service Health: ${successCount}/${totalServices} services healthy`);
  console.log(`🌐 Frontend Integration: ${frontendOk ? 'OK' : 'FAILED'}`);

  const overallSuccess = discoveredServices.size > 0 && successCount > 0 && frontendOk;

  if (overallSuccess) {
    console.log('\n🎉 Integration tests PASSED! Frontend is ready to use Eureka service discovery.');
  } else {
    console.log('\n⚠️  Integration tests FAILED. Please check the issues above.');
  }

  console.log('\n📋 Next Steps:');
  console.log('   1. Open http://localhost:5173/ in your browser');
  console.log('   2. Login with valid credentials');
  console.log('   3. Navigate to Admin → Service Registry to see discovered services');
  console.log('   4. Navigate to Admin → JWT Token Verification to check token storage');
  console.log('   5. Test all pages to verify real data integration');
}

// Run the tests
runIntegrationTests().catch(console.error);
