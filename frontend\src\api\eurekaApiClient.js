import axios from 'axios';
import eurekaService from './eurekaService';

class EurekaApiClient {
  constructor() {
    this.defaultTimeout = 10000;
  }

  // Create axios instance with Eureka service discovery
  async createServiceClient(serviceName) {
    const actualServiceName = eurekaService.getActualServiceName(serviceName);
    const serviceUrl = await eurekaService.getServiceUrl(actualServiceName);
    
    const client = axios.create({
      baseURL: serviceUrl,
      timeout: this.defaultTimeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add authentication token
    client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle common errors
    client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('authToken');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );

    return client;
  }

  // Make authenticated request to any service
  async makeRequest(serviceName, endpoint, options = {}) {
    try {
      const client = await this.createServiceClient(serviceName);
      
      const config = {
        method: options.method || 'GET',
        url: endpoint,
        ...options
      };

      const response = await client(config);
      return response;
    } catch (error) {
      console.error(`API request failed for ${serviceName}${endpoint}:`, error.message);
      throw error;
    }
  }

  // Authentication service methods
  async login(username, password) {
    return this.makeRequest('auth', '/api/auth/login', {
      method: 'POST',
      data: { username, password }
    });
  }

  async validateToken() {
    return this.makeRequest('auth', '/api/auth/validate-token', {
      method: 'POST'
    });
  }

  async getUserRoles() {
    return this.makeRequest('auth', '/api/auth/roles', {
      method: 'GET'
    });
  }

  // Flight service methods
  async getFlights() {
    return this.makeRequest('flights', '/flights');
  }

  async getFlight(id) {
    return this.makeRequest('flights', `/flights/${id}`);
  }

  async createFlight(flightData) {
    return this.makeRequest('flights', '/flights', {
      method: 'POST',
      data: flightData
    });
  }

  async updateFlight(id, flightData) {
    return this.makeRequest('flights', `/flights/${id}`, {
      method: 'PUT',
      data: flightData
    });
  }

  async deleteFlight(id) {
    return this.makeRequest('flights', `/flights/${id}`, {
      method: 'DELETE'
    });
  }

  async getFlightsByRoute(route) {
    return this.makeRequest('flights', `/flights/route/${route}`);
  }

  async getFlightsByDate(date) {
    return this.makeRequest('flights', `/flights/date/${date}`);
  }

  async getAvailableFlights() {
    return this.makeRequest('flights', '/flights/available');
  }

  // Passenger service methods
  async getPassengers() {
    return this.makeRequest('passengers', '/passengers');
  }

  async getPassenger(id) {
    return this.makeRequest('passengers', `/passengers/${id}`);
  }

  async createPassenger(passengerData) {
    return this.makeRequest('passengers', '/passengers', {
      method: 'POST',
      data: passengerData
    });
  }

  async updatePassenger(id, passengerData) {
    return this.makeRequest('passengers', `/passengers/${id}`, {
      method: 'PUT',
      data: passengerData
    });
  }

  async deletePassenger(id) {
    return this.makeRequest('passengers', `/passengers/${id}`, {
      method: 'DELETE'
    });
  }

  async getPassengersByFlight(flightId) {
    return this.makeRequest('passengers', `/passengers/flight/${flightId}`);
  }

  // User management service methods
  async getUsers() {
    return this.makeRequest('users', '/users');
  }

  async getUser(id) {
    return this.makeRequest('users', `/users/${id}`);
  }

  async createUser(userData) {
    return this.makeRequest('users', '/users', {
      method: 'POST',
      data: userData
    });
  }

  async updateUser(id, userData) {
    return this.makeRequest('users', `/users/${id}`, {
      method: 'PUT',
      data: userData
    });
  }

  async deleteUser(id) {
    return this.makeRequest('users', `/users/${id}`, {
      method: 'DELETE'
    });
  }

  // Service management methods
  async getServices() {
    return this.makeRequest('services', '/services');
  }

  async getServicesByFlight(flightId) {
    return this.makeRequest('services', `/services/flight/${flightId}`);
  }

  async getServicesByPassenger(passengerId) {
    return this.makeRequest('services', `/services/passenger/${passengerId}`);
  }

  // Travel history methods
  async getPassengerHistory(passengerId) {
    return this.makeRequest('history', `/history/passenger/${passengerId}`);
  }

  async getBookingByReference(reference) {
    return this.makeRequest('history', `/history/booking/${reference}`);
  }

  async getFlightHistory(flightId) {
    return this.makeRequest('history', `/history/flight/${flightId}`);
  }

  // Health check for all services
  async healthCheck() {
    return eurekaService.healthCheck();
  }

  // Get service registry info
  async getServiceRegistry() {
    return eurekaService.getAllServices();
  }
}

export default new EurekaApiClient();
