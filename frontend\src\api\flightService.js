import eurekaApiClient from './eurekaApiClient';

class FlightService {
  // Get all flights
  async getAllFlights() {
    try {
      const response = await eurekaApiClient.getFlights();
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch flights');
    }
  }

  // Get flight by ID
  async getFlightById(id) {
    try {
      const response = await eurekaApiClient.getFlight(id);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch flight');
    }
  }

  // Create new flight
  async createFlight(flightData) {
    try {
      const response = await eurekaApiClient.createFlight(flightData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create flight');
    }
  }

  // Update flight
  async updateFlight(id, flightData) {
    try {
      const response = await eurekaApiClient.updateFlight(id, flightData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update flight');
    }
  }

  // Delete flight
  async deleteFlight(id) {
    try {
      await eurekaApiClient.deleteFlight(id);
      return true;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete flight');
    }
  }

  // Get flights by route
  async getFlightsByRoute(route) {
    try {
      const response = await eurekaApiClient.getFlightsByRoute(route);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch flights by route');
    }
  }

  // Get flights by date
  async getFlightsByDate(date) {
    try {
      const response = await eurekaApiClient.getFlightsByDate(date);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch flights by date');
    }
  }

  // Get flight seat availability
  async getFlightSeats(flightId) {
    try {
      const response = await eurekaApiClient.makeRequest('flights', `/flights/${flightId}/seats`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch flight seats');
    }
  }

  // Get flights with available seats
  async getFlightsWithAvailableSeats() {
    try {
      const response = await eurekaApiClient.getAvailableFlights();
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch available flights');
    }
  }

  // Get flights by route with available seats
  async getFlightsByRouteWithAvailableSeats(route) {
    try {
      const response = await apiClient.get(`/flights/route/${route}/available`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch available flights by route');
    }
  }

  // Get flights by date with available seats
  async getFlightsByDateWithAvailableSeats(date) {
    try {
      const response = await apiClient.get(`/flights/date/${date}/available`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch available flights by date');
    }
  }
}

export default new FlightService();
