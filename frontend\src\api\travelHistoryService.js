import apiClient from './apiClient';

class TravelHistoryService {
  // Get passenger travel history
  async getPassengerTravelHistory(passengerId) {
    try {
      const response = await apiClient.get(`/history/passenger/${passengerId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch passenger travel history');
    }
  }

  // Get booking by reference
  async getBookingByReference(reference) {
    try {
      const response = await apiClient.get(`/history/booking/${reference}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch booking by reference');
    }
  }

  // Get flight history
  async getFlightHistory(flightId) {
    try {
      const response = await apiClient.get(`/history/flight/${flightId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch flight history');
    }
  }

  // Get recent travel history
  async getRecentTravelHistory(passengerId) {
    try {
      const response = await apiClient.get(`/history/passenger/${passengerId}/recent`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch recent travel history');
    }
  }

  // Get travel history by status
  async getTravelHistoryByStatus(passengerId, status) {
    try {
      const response = await apiClient.get(`/history/passenger/${passengerId}/status/${status}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch travel history by status');
    }
  }

  // Create travel history record
  async createTravelHistory(travelData) {
    try {
      const response = await apiClient.post('/history', travelData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create travel history');
    }
  }

  // Update travel history
  async updateTravelHistory(id, travelData) {
    try {
      const response = await apiClient.put(`/history/${id}`, travelData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update travel history');
    }
  }

  // Delete travel history
  async deleteTravelHistory(id) {
    try {
      await apiClient.delete(`/history/${id}`);
      return true;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete travel history');
    }
  }

  // Get all travel history (admin only)
  async getAllTravelHistory() {
    try {
      const response = await apiClient.get('/history');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch all travel history');
    }
  }

  // Search travel history
  async searchTravelHistory(searchParams) {
    try {
      const queryString = new URLSearchParams(searchParams).toString();
      const response = await apiClient.get(`/history/search?${queryString}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to search travel history');
    }
  }

  // Get travel statistics
  async getTravelStatistics(passengerId = null) {
    try {
      let url = '/history/stats';
      if (passengerId) {
        url += `?passengerId=${passengerId}`;
      }
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch travel statistics');
    }
  }

  // Get API info
  async getApiInfo() {
    try {
      const response = await apiClient.get('/history/info');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch API info');
    }
  }
}

export default new TravelHistoryService();
