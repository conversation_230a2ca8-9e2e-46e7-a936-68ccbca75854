import { useState, useEffect } from 'react';
import { authService } from '../api';

export default function TokenVerification() {
  const [tokenInfo, setTokenInfo] = useState(null);
  const [validationResult, setValidationResult] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadTokenInfo();
  }, []);

  const loadTokenInfo = () => {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('user');
    
    setTokenInfo({
      token: token || 'No token found',
      user: user ? JSON.parse(user) : null,
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      tokenPreview: token ? `${token.substring(0, 20)}...${token.substring(token.length - 20)}` : 'N/A'
    });
  };

  const validateToken = async () => {
    setLoading(true);
    try {
      const result = await authService.validateToken();
      setValidationResult({
        valid: true,
        data: result,
        timestamp: new Date().toLocaleString()
      });
    } catch (error) {
      setValidationResult({
        valid: false,
        error: error.message,
        timestamp: new Date().toLocaleString()
      });
    } finally {
      setLoading(false);
    }
  };

  const clearToken = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    setTokenInfo(null);
    setValidationResult(null);
    loadTokenInfo();
  };

  const decodeJWTPayload = (token) => {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (error) {
      return null;
    }
  };

  const tokenPayload = tokenInfo?.hasToken ? decodeJWTPayload(tokenInfo.token) : null;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">JWT Token Verification</h2>
        <div className="space-x-2">
          <button 
            onClick={loadTokenInfo}
            className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Refresh
          </button>
          <button 
            onClick={validateToken}
            disabled={!tokenInfo?.hasToken || loading}
            className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400"
          >
            {loading ? 'Validating...' : 'Validate Token'}
          </button>
          <button 
            onClick={clearToken}
            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Clear Token
          </button>
        </div>
      </div>

      {/* Token Storage Info */}
      <div className="bg-white rounded-lg shadow p-4">
        <h3 className="text-lg font-medium mb-3">Token Storage Status</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Token Present</label>
            <span className={`inline-block px-2 py-1 rounded text-sm ${
              tokenInfo?.hasToken ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {tokenInfo?.hasToken ? 'Yes' : 'No'}
            </span>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Token Length</label>
            <span className="text-sm">{tokenInfo?.tokenLength || 0} characters</span>
          </div>
        </div>
        
        {tokenInfo?.hasToken && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700">Token Preview</label>
            <code className="block mt-1 p-2 bg-gray-100 rounded text-xs break-all">
              {tokenInfo.tokenPreview}
            </code>
          </div>
        )}
      </div>

      {/* User Info */}
      {tokenInfo?.user && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-medium mb-3">Stored User Information</h3>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Username</label>
              <span className="text-sm">{tokenInfo.user.username}</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Name</label>
              <span className="text-sm">{tokenInfo.user.name}</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Role</label>
              <span className={`inline-block px-2 py-1 rounded text-xs ${
                tokenInfo.user.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                tokenInfo.user.role === 'passenger' ? 'bg-blue-100 text-blue-800' :
                'bg-orange-100 text-orange-800'
              }`}>
                {tokenInfo.user.role}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* JWT Payload */}
      {tokenPayload && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-medium mb-3">JWT Token Payload</h3>
          <pre className="bg-gray-100 p-3 rounded text-xs overflow-x-auto">
            {JSON.stringify(tokenPayload, null, 2)}
          </pre>
          
          {tokenPayload.exp && (
            <div className="mt-3 p-2 bg-yellow-50 rounded">
              <div className="text-sm">
                <strong>Token Expires:</strong> {new Date(tokenPayload.exp * 1000).toLocaleString()}
              </div>
              <div className="text-xs text-gray-600 mt-1">
                {tokenPayload.exp * 1000 > Date.now() ? 
                  `Valid for ${Math.round((tokenPayload.exp * 1000 - Date.now()) / 1000 / 60)} more minutes` :
                  'Token has expired'
                }
              </div>
            </div>
          )}
        </div>
      )}

      {/* Validation Result */}
      {validationResult && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-medium mb-3">Token Validation Result</h3>
          <div className="mb-2">
            <span className={`inline-block px-2 py-1 rounded text-sm ${
              validationResult.valid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {validationResult.valid ? 'Valid' : 'Invalid'}
            </span>
            <span className="ml-2 text-xs text-gray-500">
              Checked at: {validationResult.timestamp}
            </span>
          </div>
          
          {validationResult.valid ? (
            <pre className="bg-green-50 p-3 rounded text-xs overflow-x-auto">
              {JSON.stringify(validationResult.data, null, 2)}
            </pre>
          ) : (
            <div className="bg-red-50 p-3 rounded text-sm text-red-700">
              <strong>Error:</strong> {validationResult.error}
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="text-lg font-medium mb-2">How JWT Storage Works</h3>
        <ul className="text-sm space-y-1 text-gray-700">
          <li>• JWT tokens are stored in browser's localStorage</li>
          <li>• User information is stored separately for quick access</li>
          <li>• Tokens are automatically included in API requests</li>
          <li>• Invalid tokens trigger automatic logout</li>
          <li>• Tokens expire after the configured time period</li>
        </ul>
      </div>
    </div>
  );
}
