import { passengerService } from '../api';

// Cache for passengers data
let passengersCache = [];
const listeners = new Set();

// Load passengers from API
async function loadPassengers() {
  try {
    passengersCache = await passengerService.getAllPassengers();
    emit();
    return passengersCache;
  } catch (error) {
    console.error('Failed to load passengers:', error);
    throw error;
  }
}

// Get all passengers
export async function list() {
  if (passengersCache.length === 0) {
    await loadPassengers();
  }
  return passengersCache.slice();
}

// Find passenger by name
export async function findByName(name) {
  try {
    return await passengerService.getPassengersByName(name);
  } catch (error) {
    console.error('Failed to find passenger by name:', error);
    // Fallback to cache
    return passengersCache.find(p => p.name === name);
  }
}

// Find passenger by ID
export async function findById(id) {
  try {
    return await passengerService.getPassengerById(id);
  } catch (error) {
    console.error('Failed to find passenger by ID:', error);
    // Fallback to cache
    return passengersCache.find(p => p.id === id);
  }
}

// Add or update passenger
export async function addOrUpdate(p) {
  try {
    let updatedPassenger;
    if (p.id) {
      updatedPassenger = await passengerService.updatePassenger(p.id, p);
      passengersCache = passengersCache.map(x => (x.id === p.id ? updatedPassenger : x));
    } else {
      updatedPassenger = await passengerService.createPassenger(p);
      passengersCache = [...passengersCache, updatedPassenger];
    }
    emit();
    return updatedPassenger;
  } catch (error) {
    console.error('Failed to add/update passenger:', error);
    throw error;
  }
}

// Remove passenger
export async function remove(id) {
  try {
    await passengerService.deletePassenger(id);
    passengersCache = passengersCache.filter(p => p.id !== id);
    emit();
    return true;
  } catch (error) {
    console.error('Failed to remove passenger:', error);
    throw error;
  }
}

// Get passengers by flight
export async function getByFlight(flightId) {
  try {
    return await passengerService.getPassengersByFlight(flightId);
  } catch (error) {
    console.error('Failed to get passengers by flight:', error);
    throw error;
  }
}

// Search passengers by passport
export async function findByPassport(passportNumber) {
  try {
    return await passengerService.getPassengersByPassport(passportNumber);
  } catch (error) {
    console.error('Failed to find passenger by passport:', error);
    throw error;
  }
}

// Search passengers by phone
export async function findByPhone(phoneNumber) {
  try {
    return await passengerService.getPassengersByPhone(phoneNumber);
  } catch (error) {
    console.error('Failed to find passenger by phone:', error);
    throw error;
  }
}

// Event system for reactive updates
function emit() {
  listeners.forEach(fn => fn(passengersCache.slice()));
}

export function subscribe(fn) {
  listeners.add(fn);
  return () => listeners.delete(fn);
}

// Initialize passengers on module load
loadPassengers().catch(console.error);
