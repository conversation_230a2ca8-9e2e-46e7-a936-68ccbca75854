# Frontend-Microservices Integration

This document describes how the frontend has been integrated with the Eureka server and all microservices.

## Architecture Overview

The frontend now connects to all microservices through a proxy configuration in Vite, which routes API calls to the appropriate microservices:

- **Eureka Server**: http://localhost:8761 (Service Discovery)
- **Backend1 (Auth)**: http://localhost:8080 (Authentication & Authorization)
- **Flights Service**: http://localhost:8081 (Flight Management)
- **Passengers Service**: http://localhost:8082 (Passenger Management)
- **User Management**: http://localhost:8083 (User CRUD Operations)
- **Service Management**: http://localhost:8084 (Passenger Services)
- **Travel History**: http://localhost:8085 (Travel Records)

## API Integration

### 1. Proxy Configuration
The Vite development server is configured to proxy API requests to the appropriate microservices:

```javascript
// vite.config.js
server: {
  proxy: {
    '/api/auth': 'http://localhost:8080',
    '/api/flights': { target: 'http://localhost:8081', rewrite: ... },
    '/api/passengers': { target: 'http://localhost:8082', rewrite: ... },
    // ... other services
  }
}
```

### 2. API Service Layer
Created centralized API services in `/src/api/`:

- `apiClient.js` - Axios configuration with interceptors
- `authService.js` - Authentication and authorization
- `flightService.js` - Flight operations
- `passengerService.js` - Passenger operations
- `userService.js` - User management
- `serviceManagementService.js` - Passenger services
- `travelHistoryService.js` - Travel history

### 3. Authentication Flow
- JWT tokens are stored in localStorage
- Automatic token injection via axios interceptors
- Automatic logout on 401 responses
- Role-based navigation after login

## Features Implemented

### ✅ Authentication
- Login with real backend authentication
- JWT token management
- Role-based access control
- Automatic token refresh handling

### ✅ Flight Management
- Real-time flight data from microservice
- CRUD operations for flights
- Search and filtering
- Route and date-based queries

### ✅ Passenger Management
- Real passenger data integration
- Passenger CRUD operations
- Search by name, passport, phone
- Flight-based passenger queries

### ✅ Service Integration
- All microservices accessible through frontend
- Error handling and loading states
- Reactive UI updates
- Fallback to cached data when needed

## Running the Application

### Prerequisites
1. All microservices must be running:
   - Eureka Server (port 8761)
   - Backend1 (port 8080)
   - Flights Service (port 8081)
   - Passengers Service (port 8082)
   - User Management (port 8083)
   - Service Management (port 8084)
   - Travel History (port 8085)

### Start Frontend
```bash
cd frontend
npm install
npm run dev
```

The application will be available at http://localhost:5173/

### Test Integration
Run the integration test to verify all services are accessible:
```bash
node test-integration.js
```

## API Endpoints Used

### Authentication (Backend1)
- `POST /api/auth/login` - User login
- `POST /api/auth/validate-token` - Token validation
- `GET /api/auth/roles` - User roles

### Flights Service
- `GET /api/flights` - Get all flights
- `GET /api/flights/{id}` - Get flight by ID
- `POST /api/flights` - Create flight
- `PUT /api/flights/{id}` - Update flight
- `DELETE /api/flights/{id}` - Delete flight

### Passengers Service
- `GET /api/passengers` - Get all passengers
- `GET /api/passengers/{id}` - Get passenger by ID
- `POST /api/passengers` - Create passenger
- `PUT /api/passengers/{id}` - Update passenger
- `DELETE /api/passengers/{id}` - Delete passenger

### User Management
- `GET /api/users` - Get all users
- `GET /api/users/{id}` - Get user by ID
- `POST /api/users` - Create user
- `PUT /api/users/{id}` - Update user

### Service Management
- `GET /api/services` - Get all services
- `GET /api/services/flight/{flightId}` - Get services by flight
- `GET /api/services/passenger/{passengerId}` - Get services by passenger

### Travel History
- `GET /api/history/passenger/{passengerId}` - Get passenger travel history
- `GET /api/history/booking/{reference}` - Get booking by reference
- `GET /api/history/flight/{flightId}` - Get flight history

## Error Handling

The application includes comprehensive error handling:
- Network errors with retry options
- Authentication errors with automatic logout
- Loading states for better UX
- Fallback to cached data when possible
- User-friendly error messages

## Security

- JWT tokens for authentication
- Automatic token injection
- Secure token storage
- CORS configuration
- Role-based access control

## Next Steps

1. Test all functionality with real data
2. Add more comprehensive error handling
3. Implement real-time updates via WebSocket
4. Add offline support with service workers
5. Implement proper logging and monitoring
